"""
audio separation module for smart chunker

this module provides audio source separation functionality using demucs,
integrated into the smart chunker pipeline architecture.
"""

from __future__ import annotations

import traceback
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Union

import torch
import torchaudio
from demucs import separate
from pydub import AudioSegment

from smart_chunker.utils.logging import setup_logger

logger = setup_logger()

# constants
DEFAULT_FILENAME_PATTERN = "{track}_{stem}.{ext}"
DEFAULT_OVERLAP = 0.25


class SeparationModel(Enum):
    """available separation models with their characteristics."""

    HTDEMUCS = "htdemucs"  # default hybrid transformer model
    HTDEMUCS_FT = "htdemucs_ft"  # fine-tuned version (4x slower but better)
    HTDEMUCS_6S = "htdemucs_6s"  # 6 sources (adds piano and guitar)
    HDEMUCS_MMI = "hdemucs_mmi"  # hybrid demucs v3 retrained
    MDX = "mdx"  # trained on musdb hq only
    MDX_EXTRA = "mdx_extra"  # trained with extra data
    MDX_Q = "mdx_q"  # quantized mdx (smaller, slightly worse quality)
    MDX_EXTRA_Q = "mdx_extra_q"  # quantized mdx_extra


class OutputFormat(Enum):
    """supported output audio formats."""

    WAV = "wav"
    FLAC = "flac"


class StemType(Enum):
    """available stem types for separation."""

    VOCALS = "vocals"
    DRUMS = "drums"
    BASS = "bass"
    OTHER = "other"
    PIANO = "piano"  # only available with htdemucs_6s
    GUITAR = "guitar"  # only available with htdemucs_6s
    ALL = "all"


class AudioSeparationConfig:
    """configuration class for audio separation."""

    def __init__(
        self,
        model: SeparationModel = SeparationModel.HTDEMUCS,
        output_format: OutputFormat = OutputFormat.WAV,
        stems: StemType = StemType.ALL,
        output_dir: Optional[Path] = None,
        segment_length: Optional[int] = None,
        overlap: float = 0.25,
        shifts: int = 1,
        jobs: int = 1,
        device: str = "auto",
        float32: bool = False,
        int24: bool = False,
        clip_mode: str = "rescale",
        filename_pattern: str = DEFAULT_FILENAME_PATTERN,
    ):
        """initialize audio separation configuration.

        args:
            model: separation model to use
            output_format: output audio format
            stems: which stems to extract
            output_dir: output directory (defaults to data/output/audio_separation)
            segment_length: segment length in seconds for memory management
            overlap: overlap between prediction windows (0.0-1.0)
            shifts: number of random shifts for prediction averaging
            jobs: number of parallel jobs
            device: device to use ('auto', 'cpu', 'cuda')
            float32: save as float32 wav files
            int24: save as 24-bit integer wav files
            clip_mode: clipping mode ('rescale' or 'clamp')
            filename_pattern: output filename pattern
        """
        self.model = model
        self.output_format = output_format
        self.stems = stems
        self.output_dir = output_dir or Path("data/output/audio_separation")
        self.segment_length = segment_length
        self.overlap = overlap
        self.shifts = shifts
        self.jobs = jobs
        self.device = device
        self.float32 = float32
        self.int24 = int24
        self.clip_mode = clip_mode
        self.filename_pattern = filename_pattern

    def to_dict(self) -> Dict:
        """convert configuration to dictionary."""
        return {
            "model": self.model.value,
            "output_format": self.output_format.value,
            "stems": self.stems.value,
            "output_dir": str(self.output_dir),
            "segment_length": self.segment_length,
            "overlap": self.overlap,
            "shifts": self.shifts,
            "jobs": self.jobs,
            "device": self.device,
            "float32": self.float32,
            "int24": self.int24,
            "clip_mode": self.clip_mode,
            "filename_pattern": self.filename_pattern,
        }

    @classmethod
    def from_dict(cls, config_dict: Dict) -> AudioSeparationConfig:
        """create configuration from dictionary."""
        return cls(
            model=SeparationModel(config_dict.get("model", "htdemucs")),
            output_format=OutputFormat(config_dict.get("output_format", "wav")),
            stems=StemType(config_dict.get("stems", "all")),
            output_dir=Path(config_dict.get("output_dir", "data/output/audio_separation")),
            segment_length=config_dict.get("segment_length"),
            overlap=config_dict.get("overlap", DEFAULT_OVERLAP),
            shifts=config_dict.get("shifts", 1),
            jobs=config_dict.get("jobs", 1),
            device=config_dict.get("device", "auto"),
            float32=config_dict.get("float32", False),
            int24=config_dict.get("int24", False),
            clip_mode=config_dict.get("clip_mode", "rescale"),
            filename_pattern=config_dict.get("filename_pattern", DEFAULT_FILENAME_PATTERN),
        )


class AudioSeparator:
    """main audio separation class integrated into smart chunker pipeline."""

    def __init__(self, config: AudioSeparationConfig):
        """initialize the audio separator.

        args:
            config: configuration for the separation
        """
        self.config = config
        self._validate_config()
        self._setup_device()

    def _validate_config(self) -> None:
        """validate the configuration settings."""
        # validate model compatibility with stems
        if self.config.stems in [StemType.PIANO, StemType.GUITAR]:
            if self.config.model != SeparationModel.HTDEMUCS_6S:
                logger.warning(
                    "piano and guitar stems only available with htdemucs_6s model, "
                    "switching to htdemucs_6s"
                )
                self.config.model = SeparationModel.HTDEMUCS_6S

        # validate overlap range
        if not 0.0 <= self.config.overlap <= 1.0:
            raise ValueError(f"overlap must be between 0.0 and 1.0, got {self.config.overlap}")

        # validate shifts
        if self.config.shifts < 1:
            raise ValueError(f"shifts must be >= 1, got {self.config.shifts}")

        # validate jobs
        if self.config.jobs < 1:
            raise ValueError(f"jobs must be >= 1, got {self.config.jobs}")

        logger.debug("audio separation configuration validation passed")

    def _setup_device(self) -> None:
        """setup the computation device."""
        if self.config.device == "auto":
            if torch.cuda.is_available():
                self.device = "cuda"
                logger.info("using cuda device for audio separation")
            else:
                self.device = "cpu"
                logger.info("cuda not available, using cpu for audio separation")
        else:
            self.device = self.config.device
            logger.info("using {} device for audio separation", self.device)

    def validate_input_file(self, file_path: Union[str, Path]) -> Path:
        """validate input audio file.

        args:
            file_path: path to input audio file

        returns:
            validated path object

        raises:
            filenotfounderror: if file doesn't exist
            valueerror: if file format is not supported
        """
        input_path = Path(file_path)

        if not input_path.exists():
            raise FileNotFoundError(f"input file not found: {input_path}")

        if not input_path.is_file():
            raise ValueError(f"input path is not a file: {input_path}")

        # check file extension
        supported_extensions = {
            ".wav",
            ".mp3",
            ".flac",
            ".ogg",
            ".m4a",
            ".aac",
            ".mp4",
            ".avi",
            ".mkv",
            ".mov",  # video files with audio
        }

        if input_path.suffix.lower() not in supported_extensions:
            logger.warning(
                "file extension {} not in common supported formats, but will attempt processing",
                input_path.suffix,
            )

        logger.debug("input file validation passed: {}", input_path)
        return input_path

    def load_audio_info(self, file_path: Path) -> Dict:
        """load audio file information without loading the full audio.

        args:
            file_path: path to audio file

        returns:
            dictionary with audio information
        """
        try:
            # try using torchaudio first
            info = torchaudio.info(str(file_path))
            audio_info = {
                "sample_rate": info.sample_rate,
                "num_frames": info.num_frames,
                "num_channels": info.num_channels,
                "duration": info.num_frames / info.sample_rate,
                "format": "torchaudio",
            }
            logger.debug("loaded audio info using torchaudio: {}", audio_info)
            return audio_info

        except Exception as e:
            logger.debug("torchaudio failed, trying pydub: {}", str(e))

            try:
                # fallback to pydub
                audio = AudioSegment.from_file(str(file_path))
                audio_info = {
                    "sample_rate": audio.frame_rate,
                    "num_frames": len(audio.get_array_of_samples()),
                    "num_channels": audio.channels,
                    "duration": len(audio) / 1000.0,  # pydub uses milliseconds
                    "format": "pydub",
                }
                logger.debug("loaded audio info using pydub: {}", audio_info)
                return audio_info

            except Exception as e2:
                logger.error("failed to load audio info with both torchaudio and pydub")
                raise ValueError(f"unable to load audio file {file_path}: {str(e2)}")

    def estimate_memory_requirements(self, audio_info: Dict) -> Dict:
        """estimate memory requirements for separation.

        args:
            audio_info: audio information dictionary

        returns:
            dictionary with memory estimates
        """
        duration = audio_info["duration"]
        # sample_rate and num_channels could be used for more precise estimates
        # but current estimation is based primarily on duration

        # rough estimates based on demucs requirements
        base_memory_gb = 3.0  # minimum gpu memory
        memory_per_second = 0.5  # gb per second of audio

        if self.config.segment_length:
            effective_duration = min(duration, self.config.segment_length)
        else:
            effective_duration = duration

        estimated_memory = base_memory_gb + (effective_duration * memory_per_second)

        memory_info = {
            "estimated_gpu_memory_gb": estimated_memory,
            "recommended_segment_length": None,
            "can_process_full": estimated_memory <= 8.0,  # assume 8gb gpu
        }

        if not memory_info["can_process_full"]:
            # recommend segment length to fit in 6gb
            target_memory = 6.0
            recommended_duration = (target_memory - base_memory_gb) / memory_per_second
            memory_info["recommended_segment_length"] = max(10, int(recommended_duration))

        logger.debug("memory requirements estimated: {}", memory_info)
        return memory_info

    def separate_audio(self, input_file: Union[str, Path]) -> Dict:
        """separate audio file into stems.

        args:
            input_file: path to input audio file

        returns:
            dictionary with separation results and output paths

        raises:
            various exceptions for file, memory, or processing errors
        """
        try:
            # validate input
            input_path = self.validate_input_file(input_file)
            logger.info("starting audio separation for: {}", input_path.name)

            # load audio information
            audio_info = self.load_audio_info(input_path)
            logger.info(
                "audio info - duration: {:.2f}s, sample_rate: {}hz, channels: {}",
                audio_info["duration"],
                audio_info["sample_rate"],
                audio_info["num_channels"],
            )

            # estimate memory requirements
            memory_info = self.estimate_memory_requirements(audio_info)
            if not memory_info["can_process_full"] and not self.config.segment_length:
                recommended = memory_info["recommended_segment_length"]
                logger.warning(
                    "audio file may require too much memory, consider using "
                    "segment_length {} or smaller",
                    recommended,
                )

            # setup output directory
            self.config.output_dir.mkdir(parents=True, exist_ok=True)

            # build demucs command
            cmd_args = self._build_demucs_command(input_path)
            logger.debug("demucs command: {}", " ".join(cmd_args))

            # run separation
            logger.info("running audio separation with model: {}", self.config.model.value)
            separate.main(cmd_args)

            # find output files
            output_files = self._find_output_files(input_path)

            # post-process if needed
            if self.config.output_format != OutputFormat.WAV:
                output_files = self._convert_output_format(output_files)

            result = {
                "input_file": str(input_path),
                "output_directory": str(self.config.output_dir),
                "output_files": output_files,
                "model_used": self.config.model.value,
                "stems_extracted": self.config.stems.value,
                "processing_time": None,  # could be added with timing
                "audio_info": audio_info,
            }

            logger.info("audio separation completed successfully")
            logger.info("output files: {}", list(output_files.keys()))

            return result

        except Exception as e:
            logger.error("audio separation failed: {}", str(e))
            logger.debug("full traceback: {}", traceback.format_exc())
            raise

    def _build_demucs_command(self, input_path: Path) -> List[str]:
        """build demucs command arguments.

        args:
            input_path: path to input audio file

        returns:
            list of command arguments
        """
        cmd_args = []

        # model selection
        cmd_args.extend(["-n", self.config.model.value])

        # output directory (use forward slashes for cross-platform compatibility)
        cmd_args.extend(["-o", str(self.config.output_dir).replace("\\", "/")])

        # device selection
        if self.device != "auto":
            cmd_args.extend(["-d", self.device])

        # stem selection
        if self.config.stems != StemType.ALL:
            cmd_args.extend(["--two-stems", self.config.stems.value])

        # segment length for memory management
        if self.config.segment_length:
            cmd_args.extend(["--segment", str(self.config.segment_length)])

        # overlap setting
        if abs(self.config.overlap - DEFAULT_OVERLAP) > 1e-6:
            cmd_args.extend(["--overlap", str(self.config.overlap)])

        # shifts for quality improvement
        if self.config.shifts > 1:
            cmd_args.extend(["--shifts", str(self.config.shifts)])

        # parallel jobs
        if self.config.jobs > 1:
            cmd_args.extend(["-j", str(self.config.jobs)])

        # output format options
        if self.config.float32:
            cmd_args.append("--float32")
        elif self.config.int24:
            cmd_args.append("--int24")

        # clipping mode
        if self.config.clip_mode != "rescale":
            cmd_args.extend(["--clip-mode", self.config.clip_mode])

        # filename pattern
        if self.config.filename_pattern != DEFAULT_FILENAME_PATTERN:
            cmd_args.extend(["--filename", self.config.filename_pattern])

        # input file (must be last, use forward slashes for cross-platform compatibility)
        cmd_args.append(str(input_path).replace("\\", "/"))

        return cmd_args

    def _find_output_files(self, input_path: Path) -> Dict[str, Path]:
        """find generated output files.

        args:
            input_path: original input file path

        returns:
            dictionary mapping stem names to output file paths
        """
        # demucs creates subdirectories based on model and track name
        track_name = input_path.stem
        model_output_dir = self.config.output_dir / self.config.model.value / track_name

        output_files = {}

        # determine expected stems based on model and configuration
        if self.config.stems == StemType.ALL:
            if self.config.model == SeparationModel.HTDEMUCS_6S:
                expected_stems = ["vocals", "drums", "bass", "other", "piano", "guitar"]
            else:
                expected_stems = ["vocals", "drums", "bass", "other"]
        else:
            expected_stems = [self.config.stems.value]

        # find output files
        for stem in expected_stems:
            # try different possible extensions
            extensions = [".wav", ".mp3", ".flac"]
            for ext in extensions:
                potential_file = model_output_dir / f"{stem}{ext}"
                if potential_file.exists():
                    output_files[stem] = potential_file
                    break

        if not output_files:
            logger.warning("no output files found in {}", model_output_dir)
            # list actual files for debugging
            if model_output_dir.exists():
                actual_files = list(model_output_dir.glob("*"))
                logger.debug("actual files in output directory: {}", actual_files)

        return output_files

    def _convert_output_format(self, output_files: Dict[str, Path]) -> Dict[str, Path]:
        """convert output files to desired format.

        args:
            output_files: dictionary of current output files

        returns:
            dictionary of converted output files
        """
        if self.config.output_format == OutputFormat.WAV:
            return output_files  # already in wav format

        converted_files = {}

        for stem, file_path in output_files.items():
            try:
                # load audio with pydub
                audio = AudioSegment.from_file(str(file_path))

                # determine output path
                new_extension = f".{self.config.output_format.value}"
                new_path = file_path.with_suffix(new_extension)

                # export in desired format
                if self.config.output_format == OutputFormat.FLAC:
                    audio.export(str(new_path), format="flac")

                # remove original file if conversion successful
                if new_path.exists() and new_path != file_path:
                    file_path.unlink()
                    converted_files[stem] = new_path
                    logger.debug("converted {} to {}", file_path.name, new_path.name)
                else:
                    converted_files[stem] = file_path

            except Exception as e:
                logger.error(
                    "failed to convert {} to {}: {}",
                    file_path.name,
                    self.config.output_format.value,
                    str(e),
                )
                converted_files[stem] = file_path  # keep original

        return converted_files


# convenience functions for easy integration
def separate_vocals_only(input_file: Union[str, Path], output_dir: Optional[str] = None) -> Dict:
    """convenience function to separate vocals only.

    args:
        input_file: path to input audio file
        output_dir: output directory (optional)

    returns:
        separation results dictionary
    """
    config = AudioSeparationConfig(
        stems=StemType.VOCALS,
        output_dir=Path(output_dir) if output_dir else None,
    )
    separator = AudioSeparator(config)
    return separator.separate_audio(input_file)


def separate_all_stems(
    input_file: Union[str, Path], model: str = "htdemucs", output_dir: Optional[str] = None
) -> Dict:
    """convenience function to separate all stems.

    args:
        input_file: path to input audio file
        model: model to use for separation
        output_dir: output directory (optional)

    returns:
        separation results dictionary
    """
    config = AudioSeparationConfig(
        model=SeparationModel(model),
        stems=StemType.ALL,
        output_dir=Path(output_dir) if output_dir else None,
    )
    separator = AudioSeparator(config)
    return separator.separate_audio(input_file)
